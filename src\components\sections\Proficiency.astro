---
import { Picture } from "astro:assets";
import type { Sections } from "@/data/portfolio";
import { AspectRatio } from "../ui/aspect-ratio";

interface Props {
    data: Sections["proficiency"];
}

const { title, proficiency } = Astro.props.data;
---

<section id="proficiency" class="border-t border-slate-200 py-10">
    <h2 class="text-2xl font-bold text-sky-900">{title}</h2>
    <div class="mt-4 grid gap-4 sm:grid-cols-2">
        {
            proficiency.map((item) => (
                <article class="rounded border border-slate-200 bg-white p-4">
                    <AspectRatio ratio={3 / 2}>
                        <Picture
                            src={item.image}
                            alt={item.alt}
                            formats={["avif", "webp"]}
                            quality="high"
                            layout="full-width"
                            class="h-full w-full"
                        />
                    </AspectRatio>
                    <h3 class="mt-4 text-lg font-bold text-sky-900">{item.name}</h3>
                    <p class="mt-2 text-xs text-slate-600">{item.description}</p>
                </article>
            ))
        }
    </div>
</section>
