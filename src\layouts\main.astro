---
import "../styles/global.css";
import ScrollToTopButton from "@/components/ui/ScrollToTopButton.vue";

interface Props {
  title: string;
  bodyClass?: string;
}

const { title, bodyClass = "" } = Astro.props;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
  </head>
  <body class={bodyClass}>
    <slot />
    <ScrollToTopButton client:idle />
  </body>
</html>
