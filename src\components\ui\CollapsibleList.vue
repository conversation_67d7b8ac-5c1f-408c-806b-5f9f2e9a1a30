<script setup lang="ts">
import { ref, computed } from "vue";
import { ChevronDown } from "lucide-vue-next";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface Props {
  class?: string;
  showMoreText?: string;
  showLessText?: string;
  buttonClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  showMoreText: "Show more",
  showLessText: "Show less",
  buttonClass: "",
});

const isOpen = ref(false);

const buttonText = computed(() =>
  isOpen.value ? props.showLessText : props.showMoreText,
);
</script>

<template>
  <Collapsible :class="props.class" v-model:open="isOpen">
    <!-- Always visible items -->
    <slot name="visibleItems" />

    <!-- Collapsible section for additional items -->
    <CollapsibleContent>
      <slot name="hiddenItems" />
    </CollapsibleContent>

    <CollapsibleTrigger as-child>
      <Button
        variant="outline"
        :class="cn('mt-3 border-sky-200 hover:bg-sky-50', props.buttonClass)"
        :aria-expanded="isOpen"
      >
        {{ buttonText }}
        <ChevronDown
          :size="16"
          class="transition-transform duration-200"
          :class="{ 'rotate-180': isOpen }"
        />
      </Button>
    </CollapsibleTrigger>
  </Collapsible>
</template>
