---
import type { Sections } from "@/data/portfolio";
import { Image } from "astro:assets";

interface Props {
  data: Sections["experiences"];
}

const {
  title,
  employments: employmentData,
  qualifications: qualificationData,
  certifications: certificationData,
} = Astro.props.data;

const { title: employmentTitle, employments } = employmentData;
const { title: qualificationTitle, qualifications } = qualificationData;
const { title: certificationTitle, certifications } = certificationData;
---

<section
  id="experience"
  aria-labelledby="exp-title"
  class="border-t border-slate-200 py-10"
>
  <h2 id="exp-title" class="text-2xl font-bold text-sky-900">{title}</h2>

  <div class="mt-6 space-y-8">
    <!-- Employment Experience -->
    {
      employments && employments.length > 0 && (
        <section aria-labelledby="employment-title">
          <h3 id="employment-title" class="font-bold text-sky-900">
            {employmentTitle}
          </h3>
          <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
            {employments.map((job) => (
              <article class="flex h-full flex-col rounded border border-slate-200 bg-white p-4">
                <div class="flex items-start gap-4 max-[320px]:flex-col">
                  <div class="overflow-hidden rounded-md bg-slate-100">
                    <Image
                      src={job.logo}
                      alt=""
                      width={96}
                      height={96}
                      layout="fixed"
                      class="h-24 w-24 flex-shrink-0"
                    />
                  </div>

                  <div class="min-w-0 flex-1">
                    <h4 class="mb-1 text-lg font-bold text-sky-900">
                      {job.organization}
                    </h4>
                    <p class="mb-2 font-bold text-sky-600 italic">
                      {job.position}
                    </p>

                    <div class="mb-2 space-y-1 text-sm text-slate-600">
                      <p>Period: {job.period}</p>
                      <p>Location: {job.location}</p>
                      <p>Employment Type: {job.employmentType}</p>
                    </div>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </section>
      )
    }

    <!-- Academic Qualifications -->
    {
      qualifications && qualifications.length > 0 && (
        <section
          aria-labelledby="degrees-title"
          class="border-t border-slate-100 pt-6"
        >
          <h3 id="degrees-title" class="font-bold text-sky-900">
            {qualificationTitle}
          </h3>
          <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
            {qualifications.map((degree) => (
              <article class="flex h-full flex-col rounded border border-slate-200 bg-white p-4">
                <div class="flex items-start gap-4 max-[320px]:flex-col">
                  <div class="overflow-hidden rounded-md bg-slate-100">
                    <Image
                      src={degree.logo}
                      alt=""
                      width={96}
                      height={96}
                      layout="fixed"
                      class="h-24 w-24 flex-shrink-0"
                    />
                  </div>

                  <div class="min-w-0 flex-1">
                    <h4 class="mb-1 text-lg font-bold text-sky-900">
                      {degree.degree}
                    </h4>
                    <p class="mb-2 font-bold text-sky-600 italic">
                      {degree.university}
                    </p>

                    <div class="mb-2 space-y-1 text-sm text-slate-600">
                      {degree.yearCompleted && (
                        <p>
                          Year Completed:
                          {degree.yearCompleted}
                        </p>
                      )}
                      <p>Type: {degree.type}</p>
                      {degree.details && (
                        <p class="mt-2 text-sm text-slate-600">
                          {degree.details}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </section>
      )
    }

    <!-- Professional Certifications -->
    {
      certifications && certifications.length > 0 && (
        <section
          aria-labelledby="certs-title"
          class="border-t border-slate-100 pt-6"
        >
          <h3 id="certs-title" class="font-bold text-sky-900">
            {certificationTitle}
          </h3>
          <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
            {certifications.map((cert) => {
              const IconComponent = cert.icon;
              return (
                <article class="flex h-full flex-col rounded border border-slate-200 bg-white p-4">
                  <div class="flex items-start gap-4 max-[320px]:flex-col">
                    <div class="mt-1 grid h-24 w-24 flex-shrink-0 place-items-center rounded-md bg-slate-100">
                      <IconComponent size={40} class="text-sky-600" />
                    </div>

                    <div class="min-w-0 flex-1">
                      <h4 class="mb-1 text-lg font-bold text-sky-900">
                        {cert.title}
                      </h4>
                      <p class="mb-2 text-base font-bold text-sky-600 italic">
                        {cert.issued}
                      </p>

                      <div class="space-y-1 text-sm text-slate-600">
                        <p>Issuer: {cert.issuer}</p>
                        <p>Location: {cert.location}</p>
                      </div>
                    </div>
                  </div>
                </article>
              );
            })}
          </div>
        </section>
      )
    }
  </div>
</section>
