---
import { Picture } from "astro:assets";
import CollapsibleList from "@/components/ui/CollapsibleList.vue";
import type { Sections } from "@/data/portfolio";

interface Props {
    data: Sections["profile"];
}

const { name, jobTitle, contacts, profileImage, researchFieldsTitle, researchFields } = Astro.props.data;

const visibleFieldsCount = 2;
const visibleFields = researchFields.slice(0, visibleFieldsCount);
const hiddenFields = researchFields.slice(visibleFieldsCount);
---

<section id="profile" class="pt-10 pb-10 sm:pt-14">
    <div class="flex flex-col justify-center gap-8 lg:flex-row lg:gap-12">
        <!-- Right Column: Profile Information -->
        <div class="order-2">
            <h1 class="text-3xl leading-tight font-bold text-sky-900 sm:text-4xl lg:text-5xl">
                {name}
            </h1>
            <p class="mt-2 text-lg text-slate-600 italic">{jobTitle}</p>

            <dl class="mt-6 grid grid-cols-1 gap-3" aria-label="Professional contact information">
                {
                    contacts.map((contact) => {
                        const IconComponent = contact.icon;
                        return (
                            <div class="rounded-lg border border-slate-200 bg-white px-4 py-3 shadow-sm">
                                <dt class="sr-only">{contact.type}</dt>
                                <dd class="gap-2 text-sm">
                                    <IconComponent size={20} class="inline h-5 w-5 flex-shrink-0 text-sky-600" />
                                    <span class="font-bold text-sky-900">{contact.type}: </span>
                                    {contact.href ? (
                                        <a href={contact.href} class="hover:underline">
                                            {contact.value}
                                        </a>
                                    ) : (
                                        <span>{contact.value}</span>
                                    )}
                                </dd>
                            </div>
                        );
                    })
                }
            </dl>

            <div class="mt-6">
                <h2 class="text-xl font-bold text-sky-900">{researchFieldsTitle}</h2>

                <ul class="mt-3 hidden flex-wrap gap-2 md:flex" aria-label="Research specialization tags">
                    {
                        researchFields.map((field) => (
                            <li>
                                <span class="inline-block rounded-full border border-sky-200 bg-sky-50 px-4 py-2 text-sm text-sky-900 transition-colors hover:bg-sky-100">
                                    {field}
                                </span>
                            </li>
                        ))
                    }
                </ul>

                <div class="mt-3 md:hidden">
                    <CollapsibleList client:load>
                        <ul
                            slot="visibleItems"
                            class="mb-2 flex flex-wrap gap-2"
                            aria-label="Research specialization tags"
                        >
                            {
                                visibleFields.map((field) => (
                                    <li>
                                        <span class="inline-block rounded-full border border-sky-200 bg-sky-50 px-4 py-2 text-sm text-sky-900 transition-colors hover:bg-sky-100">
                                            {field}
                                        </span>
                                    </li>
                                ))
                            }
                        </ul>

                        <ul
                            slot="hiddenItems"
                            class="flex flex-wrap gap-2"
                            aria-label="Research specialization tags (continued)"
                        >
                            {
                                hiddenFields.map((field) => (
                                    <li>
                                        <span class="inline-block rounded-full border border-sky-200 bg-sky-50 px-4 py-2 text-sm text-sky-900 transition-colors hover:bg-sky-100">
                                            {field}
                                        </span>
                                    </li>
                                ))
                            }
                        </ul>
                    </CollapsibleList>
                </div>
            </div>
        </div>

        <!-- Left Column: Large Hero Profile Image -->
        <div class="order-1 lg:w-auto lg:flex-shrink-0">
            <figure
                class="grid max-w-80 place-items-center overflow-hidden rounded-2xl bg-transparent shadow-xl sm:max-w-[400px] lg:mx-auto"
            >
                {
                    profileImage && (
                        <Picture
                            src={profileImage}
                            alt={`A portrait of ${name}`}
                            widths={[320, 400, 640, 800, profileImage.width]}
                            sizes="(max-width: 640px) 320px, 400px"
                            formats={["avif", "webp"]}
                            quality="high"
                            layout="full-width"
                            priority
                        />
                    )
                }
            </figure>
        </div>
    </div>
</section>
